<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TreeOption } from 'naive-ui';
import { itemApi, skuApi, stockApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { convertToTree, showArea } from '@/utils/common';
import Tips from '@/components/common/tips.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Stock | null;
  /** the meta options */
  metaOptions?: TreeOption[];
  /** the area options */
  areas: Api.Wms.Area[];
  /** the area id */
  areaId?: number | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const unit = ref<string>('');
const precision = ref<number>(0);

const itemOptions = ref<TreeOption[]>([]);
const skuOptions = ref<TreeOption[]>([]);
const skuStocks = ref<Api.Wms.Stock[]>([]);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.Wms.Stock, 'metaId' | 'itemId' | 'skuId' | 'areaId' | 'areaPath' | 'num' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    metaId: props.rowData?.item?.metaId || 0,
    itemId: null,
    skuId: null,
    areaId: props.areaId || null,
    areaPath: [],
    num: 0,
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'itemId' | 'skuId' | 'areaId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  itemId: defaultRequiredRule,
  skuId: defaultRequiredRule,
  areaId: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增库存',
    edit: '编辑库存'
  };
  return titles[props.operateType];
});

async function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);

    if (props.rowData?.areaPath?.length)
      model.value.areaId = props.rowData?.areaPath[props.rowData?.areaPath?.length - 1];
    unit.value = props.rowData?.sku?.unit || '';
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;

  // 验证库位是否重复
  if (model.value.areaId === null) {
    window.$message?.error('库位不能为空');
    return;
  }

  try {
    await validate();
    // request
    const { error } = props.operateType === 'edit' ? await stockApi.save(model.value) : await stockApi.add(model.value);

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, async () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();

    await getItems();
    await getSkus();
    await checkSkuStock();
  }
});

async function getItems() {
  // 获取物料
  const { data, error } = await itemApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true,
    metaId: model.value.metaId
  });
  if (!error) {
    itemOptions.value = data.records;
  }
}

async function getSkus() {
  // 获取规格
  const { data, error } = await skuApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true,
    itemId: model.value.itemId
  });
  if (!error) {
    skuOptions.value = data.records;
  }
}

// 检测库存信息
async function checkSkuStock() {
  if (model.value.itemId === null) {
    skuStocks.value = [];
    return;
  }
  // 获取规格
  const { data, error } = await stockApi.list({
    _page: 1,
    _limit: 1000,
    skuId: model.value.itemId
  });
  if (!error) {
    skuStocks.value = data.records;
  }
}

async function handleMetaChange(value: number) {
  model.value.metaId = value;
  model.value.itemId = null;
  model.value.skuId = null;
  itemOptions.value = [];
  skuOptions.value = [];
  await getItems();
}

async function handleItemChange(value: number) {
  model.value.itemId = value;
  model.value.skuId = null;
  skuOptions.value = [];
  await getSkus();
}

async function handleSkuChange(value: number, option: Api.Wms.Sku) {
  unit.value = value ? option.unit || '' : '';
  precision.value = value ? option.precision : 0;
  if (value) {
    await checkSkuStock();
  } else {
    skuStocks.value = [];
  }
}

function handleAreaChange(value: number, area: Api.Wms.Area) {
  model.value.areaPath = value ? [...area.parentPath, area.id] : [];
}
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="物料分类" path="metaId">
          <NTreeSelect
            v-model:value="model.metaId"
            :options="metaOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择物料分类"
            clearable
            @update:value="handleMetaChange"
          />
        </NFormItem>
        <NFormItem label="物料" path="itemId">
          <NTreeSelect
            v-model:value="model.itemId"
            :options="itemOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择物料"
            clearable
            :disabled="itemOptions.length === 0"
            @update:value="handleItemChange"
          />
        </NFormItem>
        <NFormItem label="规格" path="skuId">
          <NTreeSelect
            v-model:value="model.skuId"
            :options="skuOptions"
            key-field="id"
            label-field="name"
            placeholder="请选择规格"
            clearable
            :disabled="skuOptions.length === 0"
            @update:value="handleSkuChange"
          />
        </NFormItem>
        <NFormItem label="库位" path="areaId">
          <NTreeSelect
            v-model:value="model.areaId"
            :options="convertToTree(areas)"
            key-field="id"
            label-field="name"
            placeholder="请选择库位"
            show-path
            clearable
            @update:value="handleAreaChange"
          />
        </NFormItem>
        <NFormItem label="库存量" path="num">
          <NInputNumber
            v-model:value="model.num"
            :min="0"
            :precision="precision"
            placeholder="请输入库存数量"
            clearable
          >
            <template #suffix>
              <span>{{ unit }}</span>
            </template>
          </NInputNumber>
        </NFormItem>
      </NForm>
      <Tips>
        <ul class="text-gray-400">
          <li>1. 同一库位和规格不可重复新增，如需修改可通过编辑调整。</li>
          <li>2. 库存数量为0时，表示清空库存。</li>
        </ul>
        <NTable v-if="skuStocks.length > 0" class="m-t-12px" bordered size="small" :single-line="false">
          <thead>
            <tr>
              <th>库位</th>
              <th>当前库存</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in skuStocks" :key="item.id">
              <td>{{ showArea(item.areaPath, areas, false).join(' / ') }}</td>
              <td>{{ item.num }}</td>
            </tr>
          </tbody>
        </NTable>
      </Tips>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
