<script setup lang="ts">
defineOptions({
  name: 'Tips'
});

defineProps<{
  title?: string;
}>();
</script>

<template>
  <div class="tips b-rd-3px p-16px shadow">
    <div class="flex items-center gap-x-4px">
      <slot name="icon"></slot>
      <span class="text-14px font-bold">{{ title || '温馨提示' }}：</span>
    </div>
    <div class="bd m-t-8px text-13px">
      <slot></slot>
    </div>
  </div>
</template>
