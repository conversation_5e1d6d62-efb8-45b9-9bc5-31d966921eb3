<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { skuApi } from '@/service/api/wms';
import QRCode from '@/components/custom/qr-code.vue';

const props = defineProps<{
  id: number;
}>();

const data = ref<Api.Wms.Sku | null>(null);

async function getData() {
  const { data: receive, error } = await skuApi.get(props.id, {
    _expand: 'item'
  });
  if (!error) {
    data.value = receive;
  }
}

onMounted(async () => {
  await getData();
});
</script>

<template>
  <div v-if="data" class="print">
    <div class="text-2xl font-bold">物料名称：{{ data.item?.name }}</div>
    <div class="m-t-16px flex justify-between">
      <div class="w-full flex flex-wrap items-center gap-y-8px">
        <div class="w-full flex">
          <div class="w-70px font-bold">规格代码：</div>
          <div>{{ data.code }}</div>
        </div>
        <div class="w-1/2 flex">
          <div class="w-70px font-bold">规格名称：</div>
          <div>{{ data.name }}</div>
        </div>
        <div class="w-1/2 flex">
          <div class="w-70px font-bold">规格单位：</div>
          <div>{{ data.unit || '-' }}</div>
        </div>
        <div class="w-1/2 flex">
          <div class="w-70px font-bold">预警库存：</div>
          <div>{{ data.min || '-' }}</div>
        </div>
        <div class="w-full flex">
          <div class="w-70px font-bold">备注：</div>
          <div>{{ data.summary || '-' }}</div>
        </div>
      </div>
      <div class="h-100px min-w-100px">
        <QRCode class="h-full" :value="data.code" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
