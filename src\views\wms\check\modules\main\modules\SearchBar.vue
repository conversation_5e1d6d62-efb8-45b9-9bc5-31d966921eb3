<script setup lang="ts">
import type { TreeOption } from 'naive-ui';
import { useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

defineProps<{
  areaOptions: TreeOption[];
}>();

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const model = defineModel<Api.Wms.CheckSearchParams>('model', { required: true });

async function reset() {
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem title="高级搜索" name="search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="100">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="单号" path="code">
              <NInput v-model:value="model.code" placeholder="请输入单号" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="类型" path="type">
              <NSelect
                v-model:value="model.type"
                :options="useDict('number').items('CheckType')"
                placeholder="请选择类型"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="库位" path="areaId">
              <NTreeSelect
                v-model:value="model.areaId"
                class="flex-1"
                :options="areaOptions"
                key-field="id"
                label-field="name"
                placeholder="请选择库位"
                show-path
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="状态" path="status">
              <NSelect
                v-model:value="model.status"
                :options="useDict('number').items('OrderStatus')"
                placeholder="请选择状态"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi suffix span="24 m:12 m:6">
              <NSpace class="w-full" justify="end">
                <NButton type="primary" @click="search">
                  <template #icon>
                    <icon-ph-magnifying-glass class="text-icon" />
                  </template>
                  搜索
                </NButton>
                <NButton @click="reset">
                  <template #icon>
                    <icon-ph-arrow-counter-clockwise class="text-icon" />
                  </template>
                  重置
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
