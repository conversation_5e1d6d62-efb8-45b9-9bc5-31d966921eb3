<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { <PERSON><PERSON><PERSON><PERSON>, NPopconfirm } from 'naive-ui';
import type { DataTableSortState, TreeOption } from 'naive-ui';
import { areaApi, metaApi, stockApi } from '@/service/api/wms';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { convertToTree, showArea } from '@/utils/common';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';
import SiderBar from './modules/SiderBar.vue';

const appStore = useAppStore();

const areas = ref<Api.Wms.Area[]>([]);

const metaOptions = ref<TreeOption[]>([]);
const areaOptions = ref<TreeOption[]>([]);
onMounted(async () => {
  // 获取分类
  const { data: metaList, error: metaError } = await metaApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true
  });
  if (!metaError) {
    metaOptions.value = convertToTree(metaList.records);
  }

  // 获取库位
  const { data: areaList, error: areaError } = await areaApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true
  });
  if (!areaError) {
    areas.value = areaList.records;
    areaOptions.value = convertToTree(areaList.records);
  }
});

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: stockApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'id',
    _order: 'asc',
    _expand: 'item,sku',
    itemId: null,
    skuId: null,
    areaId: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'itemId',
      title: '物料',
      align: 'left',
      render: row => (
        <div class="flex flex-col text-ellipsis">
          <span>{row.item?.name}</span>
          {row.item?.code && row.item?.code !== row.item?.name && (
            <span class="text-12px text-gray"># {row.item?.code}</span>
          )}
        </div>
      )
    },
    {
      key: 'skuId',
      title: '规格',
      align: 'left',
      render: row => (
        <div class="flex flex-col text-ellipsis">
          <span>{row.sku?.name}</span>
          {row.sku?.code && row.sku?.code !== row.sku?.name && (
            <span class="text-12px text-gray"># {row.sku?.code}</span>
          )}
        </div>
      )
    },
    {
      key: 'areaPath',
      title: '库位',
      align: 'left',
      render: row => (
        <div class="flex flex-col text-ellipsis">
          <span>{showArea(row.areaPath, areas.value, false).join(' / ')}</span>
          <span class="text-12px text-gray"># {showArea(row.areaPath, areas.value).join('-')}</span>
        </div>
      )
    },
    {
      key: 'num',
      title: '库存量',
      align: 'left',
      sorter: true,
      render: row => (
        <div class="flex flex-col text-ellipsis">
          <span>{row.num}</span>
          <span class="text-12px text-gray"># {row.sku?.unit}</span>
        </div>
      )
    },
    {
      key: 'createdBy',
      title: '创建人',
      align: 'left'
    },
    {
      key: 'updatedBy',
      title: '更新人',
      align: 'left'
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await stockApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await stockApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'id',
      _order: 'asc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NLayout has-sider class="sm:flex-1-hidden card-wrapper">
      <NLayoutSider :collapsed-width="0" :width="300" show-trigger="arrow-circle" bordered>
        <SiderBar v-model:value="searchParams.areaId" :meta-options="areaOptions" @search="getDataByPage" />
      </NLayoutSider>
      <NLayoutContent class="h-full">
        <NCard :bordered="false" size="small" class="h-full">
          <template #header>
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </template>
          <NDataTable
            v-model:checked-row-keys="checkedRowKeys"
            :columns="columns"
            :data="data"
            size="small"
            :flex-height="!appStore.isMobile"
            :scroll-x="962"
            :loading="loading"
            remote
            striped
            :bordered="false"
            :row-key="row => row.id"
            :pagination="pagination"
            class="b-t-1px sm:h-full b-auto"
            @update:sorter="handleSort"
          />
          <OperateDrawer
            v-model:visible="drawerVisible"
            :operate-type="operateType"
            :row-data="editingData"
            :meta-options="metaOptions"
            :areas="areas"
            :area-id="searchParams.areaId"
            @submitted="getDataByPage"
          />
        </NCard>
      </NLayoutContent>
    </NLayout>
  </div>
</template>

<style scoped></style>
